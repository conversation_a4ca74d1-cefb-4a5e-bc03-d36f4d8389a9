/**
 * @file InitializationManager Test Suite
 * @filepath shared/src/base/modules/cleanup/__tests__/InitializationManager.test.ts
 * @description Comprehensive test suite for InitializationManager
 */

import { InitializationManager, InitializationContext } from '../InitializationManager';
import { IEnhancedCleanupConfig, ICleanupCoordinatorConfig } from '../../../types/CleanupTypes';
import { ILoggingService } from '../../../LoggingMixin';
import { CleanupTemplateManager } from '../CleanupTemplateManager';
import { DependencyResolver } from '../DependencyResolver';
import { RollbackManager } from '../RollbackManager';
import { SystemOrchestrator } from '../SystemOrchestrator';

// Mock dependencies
jest.mock('../CleanupTemplateManager');
jest.mock('../DependencyResolver');
jest.mock('../RollbackManager');
jest.mock('../SystemOrchestrator');
jest.mock('../CleanupConfiguration', () => ({
  createDefaultComponentRegistry: jest.fn().mockReturnValue({
    findComponents: jest.fn().mockResolvedValue(['test-component-1', 'test-component-2']),
    getCleanupOperation: jest.fn().mockReturnValue(jest.fn()),
    registerOperation: jest.fn().mockReturnValue(true),
    hasOperation: jest.fn().mockReturnValue(true),
    listOperations: jest.fn().mockReturnValue(['operation1', 'operation2']),
    getOperationMetrics: jest.fn().mockReturnValue({
      totalOperations: 10,
      executionCount: 8,
      averageExecutionTime: 150,
      successRate: 0.8,
      lastExecution: new Date()
    })
  }),
  DEFAULT_ENHANCED_CLEANUP_CONFIG: {
    maxConcurrentOperations: 10,
    defaultTimeout: 30000,
    maxRetries: 3,
    conflictDetectionEnabled: true,
    metricsEnabled: true,
    cleanupIntervalMs: 300000,
    testMode: true,
    templateValidationEnabled: true,
    dependencyOptimizationEnabled: true,
    rollbackEnabled: true,
    maxCheckpoints: 100,
    checkpointRetentionDays: 7,
    phaseIntegrationEnabled: true,
    performanceMonitoringEnabled: true
  }
}));

describe('InitializationManager', () => {
  let initializationManager: InitializationManager;
  let mockLogger: jest.Mocked<ILoggingService>;
  let mockTemplateManager: jest.Mocked<CleanupTemplateManager>;
  let mockDependencyResolver: jest.Mocked<DependencyResolver>;
  let mockRollbackManager: jest.Mocked<RollbackManager>;
  let mockSystemOrchestrator: jest.Mocked<SystemOrchestrator>;
  let enhancedConfig: Required<IEnhancedCleanupConfig>;
  let baseConfig: Required<ICleanupCoordinatorConfig>;

  beforeEach(() => {
    // Setup mock logger
    mockLogger = {
      logInfo: jest.fn(),
      logError: jest.fn(),
      logWarning: jest.fn(),
      logDebug: jest.fn()
    };

    // Setup configurations
    enhancedConfig = {
      maxConcurrentOperations: 10,
      defaultTimeout: 30000,
      maxRetries: 3,
      conflictDetectionEnabled: true,
      metricsEnabled: true,
      cleanupIntervalMs: 300000,
      testMode: false,
      templateValidationEnabled: true,
      dependencyOptimizationEnabled: true,
      rollbackEnabled: true,
      maxCheckpoints: 100,
      checkpointRetentionDays: 7,
      phaseIntegrationEnabled: true,
      performanceMonitoringEnabled: true
    };

    baseConfig = {
      maxConcurrentOperations: 10,
      defaultTimeout: 30000,
      maxRetries: 3,
      conflictDetectionEnabled: true,
      metricsEnabled: true,
      cleanupIntervalMs: 300000,
      testMode: false
    };

    // Setup mock components
    mockTemplateManager = new CleanupTemplateManager(enhancedConfig) as jest.Mocked<CleanupTemplateManager>;
    mockDependencyResolver = new DependencyResolver(enhancedConfig) as jest.Mocked<DependencyResolver>;
    mockRollbackManager = new RollbackManager(enhancedConfig) as jest.Mocked<RollbackManager>;
    mockSystemOrchestrator = new SystemOrchestrator(enhancedConfig) as jest.Mocked<SystemOrchestrator>;

    // Mock initialize methods
    (mockTemplateManager as any).initialize = jest.fn().mockResolvedValue(undefined);
    (mockDependencyResolver as any).initialize = jest.fn().mockResolvedValue(undefined);
    (mockRollbackManager as any).initialize = jest.fn().mockResolvedValue(undefined);
    (mockSystemOrchestrator as any).initialize = jest.fn().mockResolvedValue(undefined);

    // Create InitializationManager instance
    initializationManager = new InitializationManager(enhancedConfig, baseConfig, mockLogger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initializeModularComponents', () => {
    it('should initialize all modular components successfully', async () => {
      await initializationManager.initializeModularComponents(
        mockTemplateManager,
        mockDependencyResolver,
        mockRollbackManager,
        mockSystemOrchestrator
      );

      expect(mockLogger.logInfo).toHaveBeenCalledWith('Initializing modular components', {
        templateValidationEnabled: true,
        rollbackEnabled: true,
        testMode: false
      });

      expect((mockTemplateManager as any).initialize).toHaveBeenCalled();
      expect((mockDependencyResolver as any).initialize).toHaveBeenCalled();
      expect((mockRollbackManager as any).initialize).toHaveBeenCalled();
      expect((mockSystemOrchestrator as any).initialize).toHaveBeenCalled();

      expect(mockLogger.logInfo).toHaveBeenCalledWith('All modular components initialized successfully');
    });

    it('should handle initialization errors and enhance error context', async () => {
      const initError = new Error('Template manager initialization failed');
      (mockTemplateManager as any).initialize.mockRejectedValue(initError);

      await expect(
        initializationManager.initializeModularComponents(
          mockTemplateManager,
          mockDependencyResolver,
          mockRollbackManager,
          mockSystemOrchestrator
        )
      ).rejects.toThrow();

      expect(mockLogger.logError).toHaveBeenCalledWith('Modular component initialization failed', initError);
    });
  });

  describe('initializeComponentRegistry', () => {
    it('should initialize component registry successfully', async () => {
      const registry = await initializationManager.initializeComponentRegistry();

      expect(registry).toBeDefined();
      expect(mockLogger.logInfo).toHaveBeenCalledWith('Component registry initialized successfully');
    });

    it('should handle registry initialization errors', async () => {
      // Create a new manager instance to test initialization failure
      const failingManager = new InitializationManager(enhancedConfig, baseConfig, mockLogger);

      // Mock the createDefaultComponentRegistry function to throw an error
      const mockError = new Error('Registry creation failed');
      const originalFunction = require('../CleanupConfiguration').createDefaultComponentRegistry;

      // Temporarily replace the function
      require('../CleanupConfiguration').createDefaultComponentRegistry = jest.fn().mockImplementation(() => {
        throw mockError;
      });

      try {
        await expect(failingManager.initializeComponentRegistry()).rejects.toThrow();
        expect(mockLogger.logError).toHaveBeenCalledWith('Component registry initialization failed', mockError);
      } finally {
        // Restore the original function
        require('../CleanupConfiguration').createDefaultComponentRegistry = originalFunction;
      }
    });
  });

  describe('setupEnhancedConfiguration', () => {
    it('should merge configuration with defaults', () => {
      const partialConfig = { testMode: true, maxRetries: 5 };
      const result = initializationManager.setupEnhancedConfiguration(partialConfig);

      expect(result.testMode).toBe(true);
      expect(result.maxRetries).toBe(5);
      expect(result.templateValidationEnabled).toBe(true); // from defaults
    });
  });

  describe('setupBaseConfiguration', () => {
    it('should setup base configuration with defaults', () => {
      const partialConfig = { testMode: true, maxRetries: 5 };
      const result = initializationManager.setupBaseConfiguration(partialConfig);

      expect(result.testMode).toBe(true);
      expect(result.maxRetries).toBe(5);
      expect(result.maxConcurrentOperations).toBe(10); // from defaults
    });

    it('should handle metricsEnabled mapping', () => {
      const partialConfig = { metricsEnabled: false };
      const result = initializationManager.setupBaseConfiguration(partialConfig);

      expect(result.metricsEnabled).toBe(false);
    });
  });

  describe('validateInitializationState', () => {
    it('should return true for valid configuration', () => {
      const result = initializationManager.validateInitializationState();
      expect(result).toBe(true);
    });

    it('should return false for invalid configuration', () => {
      // Create manager with incomplete config
      const incompleteConfig = { ...enhancedConfig };
      delete (incompleteConfig as any).maxConcurrentOperations;
      
      const invalidManager = new InitializationManager(incompleteConfig, baseConfig, mockLogger);
      const result = invalidManager.validateInitializationState();
      
      expect(result).toBe(false);
      expect(mockLogger.logError).toHaveBeenCalled();
    });
  });

  describe('enhanceErrorContext', () => {
    it('should enhance error with initialization context', () => {
      const originalError = new Error('Test error');
      const context: InitializationContext = {
        component: 'TestComponent',
        phase: 'test_phase',
        timestamp: '2025-08-07T16:00:00.000Z'
      };

      const enhancedError = initializationManager.enhanceErrorContext(originalError, context);

      expect(enhancedError.message).toBe('Test error');
      expect((enhancedError as any).initializationContext).toEqual(context);
      expect((enhancedError as any).component).toBe('InitializationManager');
    });
  });

  describe('getInitializationStatus', () => {
    it('should return initialization status', () => {
      const status = initializationManager.getInitializationStatus();

      expect(status).toEqual({
        configurationValid: true,
        enhancedConfigLoaded: true,
        baseConfigLoaded: true,
        registryReady: true
      });
    });
  });
});
