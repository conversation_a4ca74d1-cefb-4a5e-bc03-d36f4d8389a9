/**
 * @file AsyncErrorHandler Test Suite
 * @filepath shared/src/base/modules/cleanup/__tests__/AsyncErrorHandler.test.ts
 * @description Comprehensive test suite for AsyncErrorHandler
 */

import { 
  AsyncErrorHandler, 
  InitializationContext, 
  TemplateErrorContext, 
  ErrorContext,
  ErrorEnhancement 
} from '../AsyncErrorHandler';
import { ILoggingService } from '../../../LoggingMixin';

describe('AsyncErrorHandler', () => {
  let asyncErrorHandler: AsyncErrorHandler;
  let mockLogger: jest.Mocked<ILoggingService>;

  beforeEach(() => {
    // Setup mock logger
    mockLogger = {
      logInfo: jest.fn(),
      logError: jest.fn(),
      logWarning: jest.fn(),
      logDebug: jest.fn()
    };

    // Create AsyncErrorHandler instance
    asyncErrorHandler = new AsyncErrorHandler(mockLogger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('handleInitializationError', () => {
    it('should handle initialization errors with enhanced context', () => {
      const originalError = new Error('Initialization failed');
      const context: InitializationContext = {
        component: 'TestComponent',
        phase: 'initialization',
        timestamp: '2025-08-07T16:00:00.000Z'
      };

      const result = asyncErrorHandler.handleInitializationError(originalError, context);

      expect(mockLogger.logError).toHaveBeenCalledWith('Initialization failed', originalError);
      expect(result).toBeInstanceOf(Error);
      expect(result.message).toBe('Initialization failed');
      expect((result as any).component).toBe('AsyncErrorHandler');
    });

    it('should handle non-Error objects', () => {
      const context: InitializationContext = {
        component: 'TestComponent',
        phase: 'initialization',
        timestamp: '2025-08-07T16:00:00.000Z'
      };

      const result = asyncErrorHandler.handleInitializationError('String error' as any, context);

      expect(result).toBeInstanceOf(Error);
      expect(result.message).toBe('String error');
    });
  });

  describe('handleTemplateError', () => {
    it('should handle template execution errors', () => {
      const originalError = new Error('Template execution failed');
      const templateContext: TemplateErrorContext = {
        templateId: 'test-template',
        targetComponents: ['comp1', 'comp2'],
        parametersCount: 5,
        component: 'TemplateManager',
        phase: 'execution'
      };

      const result = asyncErrorHandler.handleTemplateError(originalError, templateContext);

      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Template execution failed with timing context',
        expect.any(Error)
      );
      expect(result).toBeInstanceOf(Error);
      expect(result.message).toBe('Template execution failed');
    });
  });

  describe('handleAsyncOperationError', () => {
    it('should handle async operation errors with isolation', () => {
      const error = new Error('Async operation failed');
      const operationId = 'test-operation-123';

      asyncErrorHandler.handleAsyncOperationError(error, operationId);

      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Operation failed but coordinator remains operational',
        error,
        {
          operationId,
          errorType: 'async_operation_error',
          isolated: true
        }
      );
    });
  });

  describe('handleQueueProcessingError', () => {
    it('should handle queue processing errors', () => {
      const error = new Error('Queue processing failed');

      asyncErrorHandler.handleQueueProcessingError(error);

      expect(mockLogger.logWarning).toHaveBeenCalledWith(
        'Some operations failed during batch processing',
        {
          error: 'Queue processing failed',
          errorType: 'queue_processing_error'
        }
      );
    });

    it('should handle non-Error objects in queue processing', () => {
      asyncErrorHandler.handleQueueProcessingError('String error' as any);

      expect(mockLogger.logWarning).toHaveBeenCalledWith(
        'Some operations failed during batch processing',
        {
          error: 'String error',
          errorType: 'queue_processing_error'
        }
      );
    });
  });

  describe('handleOperationExecutionError', () => {
    it('should handle operation execution errors', () => {
      const error = new Error('Operation execution failed');
      const operationId = 'exec-op-456';

      asyncErrorHandler.handleOperationExecutionError(error, operationId);

      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Cleanup operation failed',
        error,
        {
          operationId,
          errorType: 'operation_execution_error'
        }
      );
    });
  });

  describe('enhanceErrorContext', () => {
    it('should enhance error context with timing information', () => {
      const originalError = new Error('Test error');
      const context: ErrorContext = {
        component: 'TestComponent',
        phase: 'test_phase',
        customData: 'test_value'
      };

      const result = asyncErrorHandler.enhanceErrorContext(originalError, context);

      expect(result).toBeInstanceOf(Error);
      expect(result.message).toBe('Test error');
      expect(result.name).toBe(originalError.name);
      expect(result.stack).toBe(originalError.stack);
      expect((result as any).resilientContext).toEqual(context);
      expect((result as any).component).toBe('AsyncErrorHandler');
      expect((result as any).timestamp).toBeDefined();
    });
  });

  describe('handleRollbackError', () => {
    it('should handle rollback errors', () => {
      const rollbackError = new Error('Rollback failed');
      const operationId = 'rollback-op-789';
      const checkpointId = 'checkpoint-123';

      asyncErrorHandler.handleRollbackError(rollbackError, operationId, checkpointId);

      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Rollback failed after cleanup failure',
        rollbackError,
        {
          operationId,
          checkpointId,
          errorType: 'rollback_error'
        }
      );
    });
  });

  describe('handleEnhancedCleanupError', () => {
    it('should handle enhanced cleanup operation errors', () => {
      const error = new Error('Enhanced cleanup failed');
      const operationId = 'enhanced-op-101';
      const checkpointId = 'checkpoint-456';

      const result = asyncErrorHandler.handleEnhancedCleanupError(error, operationId, checkpointId);

      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Enhanced cleanup operation failed',
        error,
        {
          operationId,
          checkpointId,
          errorType: 'enhanced_cleanup_error'
        }
      );
      expect(result).toBe(error);
    });

    it('should handle non-Error objects in enhanced cleanup', () => {
      const result = asyncErrorHandler.handleEnhancedCleanupError('String error' as any, 'op-id');

      expect(result).toBeInstanceOf(Error);
      expect(result.message).toBe('String error');
    });
  });

  describe('handleTimingInfrastructureError', () => {
    it('should handle timing infrastructure errors', () => {
      const timingError = new Error('Timing infrastructure error');

      asyncErrorHandler.handleTimingInfrastructureError(timingError);

      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Error during resilient timing infrastructure shutdown',
        timingError
      );
    });
  });

  describe('handleTemplateRegistrationError', () => {
    it('should handle template execution registration errors', () => {
      const registrationError = new Error('Registration failed');
      const templateId = 'template-123';
      const executionId = 'exec-456';
      const timingReliable = true;
      const executionTime = 150;

      asyncErrorHandler.handleTemplateRegistrationError(
        registrationError,
        templateId,
        executionId,
        timingReliable,
        executionTime
      );

      expect(mockLogger.logWarning).toHaveBeenCalledWith(
        'Template execution registration failed',
        {
          templateId,
          executionId,
          timingReliable,
          executionTime,
          error: 'Registration failed'
        }
      );
    });
  });

  describe('handleTimingReliabilityError', () => {
    it('should handle timing reliability metrics collection errors', () => {
      const error = new Error('Reliability metrics failed');

      asyncErrorHandler.handleTimingReliabilityError(error);

      expect(mockLogger.logWarning).toHaveBeenCalledWith(
        'Timing reliability metrics collection failed, using defaults',
        {
          error: 'Reliability metrics failed'
        }
      );
    });
  });

  describe('handleGeneralAsyncError', () => {
    it('should handle general async operation errors', () => {
      const error = new Error('General async error');
      const context = { operationType: 'test', retryCount: 2 };

      const result = asyncErrorHandler.handleGeneralAsyncError(error, context);

      expect(mockLogger.logError).toHaveBeenCalledWith('General async operation error', result);
      expect((result as any).resilientContext.operationType).toBe('test');
      expect((result as any).resilientContext.retryCount).toBe(2);
    });
  });

  describe('isRecoverableError', () => {
    it('should identify recoverable errors', () => {
      const timeoutError = new Error('Operation timeout occurred');
      const networkError = new Error('Network connection failed');
      const retryError = new Error('Retry limit exceeded');
      const fatalError = new Error('Fatal system error');

      expect(asyncErrorHandler.isRecoverableError(timeoutError)).toBe(true);
      expect(asyncErrorHandler.isRecoverableError(networkError)).toBe(true);
      expect(asyncErrorHandler.isRecoverableError(retryError)).toBe(true);
      expect(asyncErrorHandler.isRecoverableError(fatalError)).toBe(false);
    });
  });

  describe('getErrorSeverity', () => {
    it('should classify error severity correctly', () => {
      const criticalError = new Error('Critical system failure');
      const fatalError = new Error('Fatal error occurred');
      const timeoutError = new Error('Request timeout');
      const networkError = new Error('Network error');
      const warningError = new Error('Warning: deprecated method');
      const retryError = new Error('Retry attempt failed');
      const unknownError = new Error('Unknown error');

      expect(asyncErrorHandler.getErrorSeverity(criticalError)).toBe('critical');
      expect(asyncErrorHandler.getErrorSeverity(fatalError)).toBe('critical');
      expect(asyncErrorHandler.getErrorSeverity(timeoutError)).toBe('medium');
      expect(asyncErrorHandler.getErrorSeverity(networkError)).toBe('medium');
      expect(asyncErrorHandler.getErrorSeverity(warningError)).toBe('low');
      expect(asyncErrorHandler.getErrorSeverity(retryError)).toBe('low');
      expect(asyncErrorHandler.getErrorSeverity(unknownError)).toBe('high');
    });
  });
});
