/**
 * @file TimingInfrastructureManager Test Suite
 * @filepath shared/src/base/modules/cleanup/__tests__/TimingInfrastructureManager.test.ts
 * @description Comprehensive test suite for TimingInfrastructureManager
 */

import { TimingInfrastructureManager, TimingMetrics, TimingReliabilityMetrics } from '../TimingInfrastructureManager';
import { IEnhancedCleanupConfig } from '../../../types/CleanupTypes';
import { ILoggingService } from '../../../LoggingMixin';

// Mock dependencies
jest.mock('../../../utils/ResilientTiming', () => ({
  ResilientTimer: jest.fn().mockImplementation(() => ({
    start: jest.fn().mockReturnValue({
      start: jest.fn(),
      end: jest.fn().mockReturnValue({
        duration: 100,
        reliable: true,
        fallbackUsed: false,
        timestamp: Date.now()
      })
    })
  }))
}));

jest.mock('../../../utils/ResilientMetrics', () => ({
  ResilientMetricsCollector: jest.fn().mockImplementation(() => ({
    createSnapshot: jest.fn().mockReturnValue({
      metrics: new Map([
        ['executeOperation', { value: 50 }],
        ['processQueue', { value: 30 }]
      ])
    }),
    recordTiming: jest.fn(),
    reset: jest.fn()
  }))
}));

describe('TimingInfrastructureManager', () => {
  let timingManager: TimingInfrastructureManager;
  let mockLogger: jest.Mocked<ILoggingService>;
  let enhancedConfig: Required<IEnhancedCleanupConfig>;

  beforeEach(() => {
    // Setup mock logger
    mockLogger = {
      logInfo: jest.fn(),
      logError: jest.fn(),
      logWarning: jest.fn(),
      logDebug: jest.fn()
    };

    // Setup configuration
    enhancedConfig = {
      maxConcurrentOperations: 10,
      defaultTimeout: 30000,
      maxRetries: 3,
      conflictDetectionEnabled: true,
      metricsEnabled: true,
      cleanupIntervalMs: 300000,
      testMode: false,
      templateValidationEnabled: true,
      dependencyOptimizationEnabled: true,
      rollbackEnabled: true,
      maxCheckpoints: 100,
      checkpointRetentionDays: 7,
      phaseIntegrationEnabled: true,
      performanceMonitoringEnabled: true
    };

    // Create TimingInfrastructureManager instance
    timingManager = new TimingInfrastructureManager(mockLogger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initialize', () => {
    it('should initialize timing infrastructure successfully', async () => {
      await timingManager.initialize(enhancedConfig);

      expect(mockLogger.logInfo).toHaveBeenCalledWith(
        'Resilient timing infrastructure initialized successfully',
        {
          timerFallbacksEnabled: true,
          metricsCollectionEnabled: true,
          performanceTarget: 'enterprise'
        }
      );

      expect(timingManager.isInitialized()).toBe(true);
    });

    it('should handle initialization errors', async () => {
      // Create a new manager instance to test initialization failure
      const failingManager = new TimingInfrastructureManager(mockLogger);

      // Mock the ResilientTimer constructor to throw an error
      const mockError = new Error('Timer initialization failed');
      const originalResilientTimer = require('../../../utils/ResilientTiming').ResilientTimer;

      // Temporarily replace the constructor
      const mockConstructor = jest.fn().mockImplementation(() => {
        throw mockError;
      });
      require('../../../utils/ResilientTiming').ResilientTimer = mockConstructor;

      try {
        await expect(failingManager.initialize(enhancedConfig)).rejects.toThrow(mockError);
        expect(mockLogger.logError).toHaveBeenCalledWith(
          'Failed to initialize timing infrastructure',
          mockError
        );
      } finally {
        // Restore the original constructor
        require('../../../utils/ResilientTiming').ResilientTimer = originalResilientTimer;
      }
    });
  });

  describe('createTimingContext', () => {
    it('should create timing context when initialized', async () => {
      await timingManager.initialize(enhancedConfig);

      const context = timingManager.createTimingContext();
      expect(context).toBeDefined();
      expect(context).toHaveProperty('start');
      expect(context).toHaveProperty('end');
    });

    it('should throw error when not initialized', () => {
      expect(() => timingManager.createTimingContext()).toThrow(
        'TimingInfrastructureManager not initialized'
      );
    });
  });

  describe('recordTiming', () => {
    it('should record timing when initialized', async () => {
      await timingManager.initialize(enhancedConfig);
      
      const timingResult = {
        duration: 100,
        reliable: true,
        fallbackUsed: false,
        timestamp: Date.now(),
        method: 'performance' as const
      };

      timingManager.recordTiming('test-operation', timingResult);
      // Should not throw and should log nothing for successful recording
    });

    it('should log warning when not initialized', () => {
      const timingResult = {
        duration: 100,
        reliable: true,
        fallbackUsed: false,
        timestamp: Date.now(),
        method: 'performance' as const
      };

      timingManager.recordTiming('test-operation', timingResult);
      
      expect(mockLogger.logWarning).toHaveBeenCalledWith(
        'Attempted to record timing before initialization',
        { operation: 'test-operation' }
      );
    });
  });

  describe('getTimingMetrics', () => {
    it('should return timing metrics when initialized', async () => {
      await timingManager.initialize(enhancedConfig);
      
      const metrics = await timingManager.getTimingMetrics();
      
      expect(metrics).toEqual({
        operationCount: 2, // Two operations in mock
        totalDuration: 80, // 50 + 30 from mock data
        averageDuration: 40, // 80 / 2
        coordinationOverhead: 30 // processQueue value from mock
      });
    });

    it('should return default metrics when not initialized', async () => {
      const metrics = await timingManager.getTimingMetrics();
      
      expect(metrics).toEqual({
        operationCount: 0,
        totalDuration: 0,
        averageDuration: 0,
        coordinationOverhead: 0
      });
    });

    it('should calculate metrics with recorded operations', async () => {
      await timingManager.initialize(enhancedConfig);
      
      // Mock metrics collector to return sample data
      const mockSnapshot = {
        metrics: new Map([
          ['executeOperation_test', { value: 50, timestamp: Date.now(), reliable: true, source: 'measured' }],
          ['scheduleCleanup', { value: 10, timestamp: Date.now(), reliable: true, source: 'measured' }]
        ]),
        timestamp: Date.now(),
        reliable: true,
        warnings: []
      };

      // Access private _metricsCollector and mock its createSnapshot method
      (timingManager as any)._metricsCollector = {
        createSnapshot: jest.fn().mockReturnValue(mockSnapshot)
      };

      const metrics = await timingManager.getTimingMetrics();
      
      expect(metrics.operationCount).toBe(2);
      expect(metrics.totalDuration).toBeGreaterThan(0);
      expect(metrics.coordinationOverhead).toBeGreaterThan(0);
    });
  });

  describe('getTimingReliabilityMetrics', () => {
    it('should return reliability metrics when initialized', async () => {
      await timingManager.initialize(enhancedConfig);
      
      const metrics = await timingManager.getTimingReliabilityMetrics();
      
      expect(metrics).toEqual({
        fallbacksUsed: 0,
        reliabilityScore: 0.95,
        unreliableOperations: 0
      });
    });

    it('should return default metrics when not initialized', async () => {
      const metrics = await timingManager.getTimingReliabilityMetrics();
      
      expect(metrics).toEqual({
        fallbacksUsed: 0,
        reliabilityScore: 0.90,
        unreliableOperations: 0
      });
    });

    it('should handle errors gracefully', async () => {
      await timingManager.initialize(enhancedConfig);
      
      // Mock metrics collector to throw error
      (timingManager as any)._metricsCollector = {
        createSnapshot: jest.fn().mockImplementation(() => {
          throw new Error('Metrics collection failed');
        })
      };

      const metrics = await timingManager.getTimingReliabilityMetrics();
      
      expect(metrics).toEqual({
        fallbacksUsed: 0,
        reliabilityScore: 0.90,
        unreliableOperations: 0
      });

      expect(mockLogger.logWarning).toHaveBeenCalledWith(
        'Timing reliability metrics collection failed, using defaults',
        { error: 'Metrics collection failed' }
      );
    });
  });

  describe('clearTimingMetrics', () => {
    it('should clear metrics when initialized', async () => {
      await timingManager.initialize(enhancedConfig);
      
      timingManager.clearTimingMetrics();
      // Should not throw
    });

    it('should handle clear when not initialized', () => {
      timingManager.clearTimingMetrics();
      // Should not throw
    });
  });

  describe('shutdown', () => {
    it('should shutdown timing infrastructure when initialized', async () => {
      await timingManager.initialize(enhancedConfig);
      
      // Mock metrics collector
      const mockSnapshot = {
        metrics: new Map([['test', { value: 1, timestamp: Date.now(), reliable: true, source: 'measured' }]]),
        timestamp: Date.now(),
        reliable: true,
        warnings: []
      };

      (timingManager as any)._metricsCollector = {
        createSnapshot: jest.fn().mockReturnValue(mockSnapshot),
        reset: jest.fn()
      };

      timingManager.shutdown();

      expect(mockLogger.logInfo).toHaveBeenCalledWith(
        'Final resilient metrics snapshot',
        {
          totalMetrics: 1,
          reliable: true,
          warnings: 0
        }
      );

      expect(mockLogger.logInfo).toHaveBeenCalledWith(
        'Resilient timing infrastructure shutdown completed successfully'
      );

      expect(timingManager.isInitialized()).toBe(false);
    });

    it('should handle shutdown when not initialized', () => {
      timingManager.shutdown();
      // Should not throw
    });

    it('should handle shutdown errors', async () => {
      await timingManager.initialize(enhancedConfig);
      
      // Mock metrics collector to throw error
      (timingManager as any)._metricsCollector = {
        createSnapshot: jest.fn().mockImplementation(() => {
          throw new Error('Shutdown error');
        })
      };

      timingManager.shutdown();

      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Error during resilient timing infrastructure shutdown',
        expect.any(Error)
      );
    });
  });

  describe('getMetricsSnapshot', () => {
    it('should return metrics snapshot when initialized', async () => {
      await timingManager.initialize(enhancedConfig);
      
      const mockSnapshot = {
        metrics: new Map(),
        timestamp: Date.now(),
        reliable: true,
        warnings: []
      };

      (timingManager as any)._metricsCollector = {
        createSnapshot: jest.fn().mockReturnValue(mockSnapshot)
      };

      const snapshot = timingManager.getMetricsSnapshot();
      expect(snapshot).toBe(mockSnapshot);
    });

    it('should return null when not initialized', () => {
      const snapshot = timingManager.getMetricsSnapshot();
      expect(snapshot).toBeNull();
    });
  });
});
