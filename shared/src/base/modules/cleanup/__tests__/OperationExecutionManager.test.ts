/**
 * @file OperationExecutionManager Test Suite
 * @filepath shared/src/base/modules/cleanup/__tests__/OperationExecutionManager.test.ts
 * @description Comprehensive test suite for OperationExecutionManager
 */

import { OperationExecutionManager } from '../OperationExecutionManager';
import { TimingInfrastructureManager } from '../TimingInfrastructureManager';
import { ICleanupCoordinatorConfig, ICleanupMetrics } from '../../../CleanupCoordinatorEnhanced';
import { ICleanupOperation, CleanupStatus, CleanupOperationType, CleanupPriority } from '../../../CleanupCoordinatorEnhanced';
import { ILoggingService } from '../../../LoggingMixin';

// Mock dependencies
jest.mock('../TimingInfrastructureManager');

describe('OperationExecutionManager', () => {
  let operationExecutionManager: OperationExecutionManager;
  let mockLogger: jest.Mocked<ILoggingService>;
  let mockTimingManager: jest.Mocked<TimingInfrastructureManager>;
  let config: Required<ICleanupCoordinatorConfig>;
  let mockMetrics: ICleanupMetrics;

  beforeEach(() => {
    // Setup mock logger
    mockLogger = {
      logInfo: jest.fn(),
      logError: jest.fn(),
      logWarning: jest.fn(),
      logDebug: jest.fn()
    };

    // Setup mock timing manager
    mockTimingManager = {
      createTimingContext: jest.fn().mockReturnValue({
        end: jest.fn().mockReturnValue({
          duration: 100,
          reliable: true,
          fallbackUsed: false,
          timestamp: Date.now(),
          method: 'performance'
        })
      }),
      recordTiming: jest.fn(),
      isInitialized: jest.fn().mockReturnValue(true)
    } as any;

    // Setup configuration
    config = {
      maxConcurrentOperations: 5,
      defaultTimeout: 30000,
      maxRetries: 3,
      conflictDetectionEnabled: true,
      metricsEnabled: true,
      cleanupIntervalMs: 300000,
      testMode: false
    };

    // Setup mock metrics
    mockMetrics = {
      totalOperations: 0,
      queuedOperations: 0,
      runningOperations: 0,
      completedOperations: 0,
      failedOperations: 0,
      averageExecutionTime: 0,
      longestOperation: 0,
      operationsByType: {
        'timer-cleanup': 0,
        'event-handler-cleanup': 0,
        'buffer-cleanup': 0,
        'resource-cleanup': 0,
        'memory-cleanup': 0,
        'shutdown-cleanup': 0
      } as Record<CleanupOperationType, number>,
      operationsByPriority: {
        1: 0, // LOW
        2: 0, // NORMAL
        3: 0, // HIGH
        4: 0, // CRITICAL
        5: 0  // EMERGENCY
      } as Record<CleanupPriority, number>,
      conflictsPrevented: 0,
      lastCleanupTime: null
    };

    // Create OperationExecutionManager instance
    operationExecutionManager = new OperationExecutionManager(config, mockTimingManager, mockLogger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('executeOperation', () => {
    it('should execute operation successfully in test mode', async () => {
      config.testMode = true;
      const operation: ICleanupOperation = {
        id: 'test-op-1',
        type: CleanupOperationType.RESOURCE_CLEANUP,
        priority: CleanupPriority.NORMAL,
        status: CleanupStatus.QUEUED,
        componentId: 'test-component',
        operation: jest.fn().mockResolvedValue(undefined),
        createdAt: new Date()
      };

      await operationExecutionManager.executeOperation(operation);

      expect(operation.status).toBe(CleanupStatus.COMPLETED);
      expect(operation.completedAt).toBeDefined();
      expect(operation.operation).toHaveBeenCalled();
      expect(mockTimingManager.recordTiming).toHaveBeenCalled();
      expect(mockLogger.logInfo).toHaveBeenCalledWith(
        'Cleanup operation completed',
        expect.objectContaining({
          operationId: 'test-op-1',
          type: CleanupOperationType.RESOURCE_CLEANUP,
          componentId: 'test-component'
        })
      );
    });

    it('should execute operation with timeout in production mode', async () => {
      config.testMode = false;
      const operation: ICleanupOperation = {
        id: 'test-op-2',
        type: CleanupOperationType.RESOURCE_CLEANUP,
        priority: CleanupPriority.NORMAL,
        status: CleanupStatus.QUEUED,
        componentId: 'test-component',
        operation: jest.fn().mockResolvedValue(undefined),
        timeout: 1000,
        createdAt: new Date()
      };

      await operationExecutionManager.executeOperation(operation);

      expect(operation.status).toBe(CleanupStatus.COMPLETED);
      expect(mockTimingManager.recordTiming).toHaveBeenCalled();
    });

    it('should handle operation timeout', async () => {
      // Use fake timers for this test
      jest.useFakeTimers();

      config.testMode = false;
      const operation: ICleanupOperation = {
        id: 'test-op-timeout',
        type: CleanupOperationType.RESOURCE_CLEANUP,
        priority: CleanupPriority.NORMAL,
        status: CleanupStatus.QUEUED,
        componentId: 'test-component',
        operation: jest.fn().mockImplementation(() => new Promise(resolve => setTimeout(resolve, 2000))),
        timeout: 100,
        createdAt: new Date()
      };

      const executePromise = operationExecutionManager.executeOperation(operation);

      // Fast-forward time to trigger timeout
      jest.advanceTimersByTime(150);

      await expect(executePromise).rejects.toThrow('Operation timeout');
      expect(operation.status).toBe(CleanupStatus.FAILED);
      expect(operation.error).toBeDefined();

      // Restore real timers
      jest.useRealTimers();
    });

    it('should handle operation execution errors', async () => {
      const operationError = new Error('Operation failed');
      const operation: ICleanupOperation = {
        id: 'test-op-error',
        type: CleanupOperationType.RESOURCE_CLEANUP,
        priority: CleanupPriority.NORMAL,
        status: CleanupStatus.QUEUED,
        componentId: 'test-component',
        operation: jest.fn().mockRejectedValue(operationError),
        createdAt: new Date()
      };

      await expect(operationExecutionManager.executeOperation(operation)).rejects.toThrow(operationError);
      expect(operation.status).toBe(CleanupStatus.FAILED);
      expect(operation.error).toBe(operationError);
      expect(mockTimingManager.recordTiming).toHaveBeenCalledWith(
        'executeOperation_failed_resource-cleanup',
        expect.any(Object)
      );
    });
  });

  describe('processOperationWithErrorIsolation', () => {
    it('should process operation successfully', async () => {
      const operation: ICleanupOperation = {
        id: 'test-op-isolation',
        type: CleanupOperationType.RESOURCE_CLEANUP,
        priority: CleanupPriority.NORMAL,
        status: CleanupStatus.QUEUED,
        componentId: 'test-component',
        operation: jest.fn().mockResolvedValue(undefined),
        createdAt: new Date()
      };

      const runningOperations = new Set<string>();
      
      await operationExecutionManager.processOperationWithErrorIsolation(
        operation,
        runningOperations,
        mockMetrics
      );

      expect(mockMetrics.completedOperations).toBe(1);
    });

    it('should isolate operation errors', async () => {
      const operationError = new Error('Isolated operation error');
      const operation: ICleanupOperation = {
        id: 'test-op-isolation-error',
        type: CleanupOperationType.RESOURCE_CLEANUP,
        priority: CleanupPriority.NORMAL,
        status: CleanupStatus.QUEUED,
        componentId: 'test-component',
        operation: jest.fn().mockRejectedValue(operationError),
        createdAt: new Date()
      };

      const runningOperations = new Set<string>(['test-op-isolation-error']);
      
      await operationExecutionManager.processOperationWithErrorIsolation(
        operation,
        runningOperations,
        mockMetrics
      );

      expect(operation.status).toBe(CleanupStatus.FAILED);
      expect(operation.error).toBe(operationError);
      expect(mockMetrics.failedOperations).toBe(1);
      expect(runningOperations.has('test-op-isolation-error')).toBe(false);
      expect(mockLogger.logError).toHaveBeenCalledWith(
        'Operation failed but coordinator remains operational',
        operationError,
        expect.objectContaining({
          operationId: 'test-op-isolation-error'
        })
      );
    });

    it('should ensure operation cleanup in finally block', async () => {
      const operation: ICleanupOperation = {
        id: 'test-op-cleanup',
        type: CleanupOperationType.RESOURCE_CLEANUP,
        priority: CleanupPriority.NORMAL,
        status: CleanupStatus.QUEUED,
        componentId: 'test-component',
        operation: jest.fn().mockRejectedValue(new Error('Test error')),
        createdAt: new Date()
      };

      const runningOperations = new Set<string>(['test-op-cleanup']);
      
      await operationExecutionManager.processOperationWithErrorIsolation(
        operation,
        runningOperations,
        mockMetrics
      );

      expect(runningOperations.has('test-op-cleanup')).toBe(false);
    });
  });

  describe('processQueueInternal', () => {
    it('should process queue in test mode', async () => {
      config.testMode = true;
      const operations = new Map<string, ICleanupOperation>();
      const operationQueue: ICleanupOperation[] = [
        {
          id: 'queue-op-1',
          type: CleanupOperationType.RESOURCE_CLEANUP,
          priority: CleanupPriority.NORMAL,
          status: CleanupStatus.QUEUED,
          componentId: 'test-component',
          operation: jest.fn().mockResolvedValue(undefined),
          createdAt: new Date()
        }
      ];
      const runningOperations = new Set<string>();

      await operationExecutionManager.processQueueInternal(
        operationQueue,
        runningOperations,
        operations,
        mockMetrics
      );

      expect(operationQueue).toHaveLength(0);
      expect(mockMetrics.runningOperations).toBe(0);
    });

    it('should respect max concurrency limits', async () => {
      config.maxConcurrentOperations = 2;
      const operations = new Map<string, ICleanupOperation>();
      const operationQueue: ICleanupOperation[] = [
        {
          id: 'queue-op-1',
          type: CleanupOperationType.RESOURCE_CLEANUP,
          priority: CleanupPriority.NORMAL,
          status: CleanupStatus.QUEUED,
          componentId: 'test-component-1',
          operation: jest.fn().mockResolvedValue(undefined),
          createdAt: new Date()
        },
        {
          id: 'queue-op-2',
          type: CleanupOperationType.RESOURCE_CLEANUP,
          priority: CleanupPriority.NORMAL,
          status: CleanupStatus.QUEUED,
          componentId: 'test-component-2',
          operation: jest.fn().mockResolvedValue(undefined),
          createdAt: new Date()
        },
        {
          id: 'queue-op-3',
          type: CleanupOperationType.RESOURCE_CLEANUP,
          priority: CleanupPriority.NORMAL,
          status: CleanupStatus.QUEUED,
          componentId: 'test-component-3',
          operation: jest.fn().mockResolvedValue(undefined),
          createdAt: new Date()
        }
      ];
      const runningOperations = new Set<string>();

      await operationExecutionManager.processQueueInternal(
        operationQueue,
        runningOperations,
        operations,
        mockMetrics
      );

      // Should process only up to max concurrency
      expect(operationQueue.length).toBeLessThanOrEqual(1);
    });
  });

  describe('waitForOperationCompletion', () => {
    it('should wait for operation completion', async () => {
      const operation: ICleanupOperation = {
        id: 'wait-op-1',
        type: CleanupOperationType.RESOURCE_CLEANUP,
        priority: CleanupPriority.NORMAL,
        status: CleanupStatus.COMPLETED,
        componentId: 'test-component',
        operation: jest.fn(),
        createdAt: new Date(),
        startedAt: new Date(),
        completedAt: new Date()
      };

      const operations = new Map([['wait-op-1', operation]]);

      const result = await operationExecutionManager.waitForOperationCompletion('wait-op-1', operations);

      expect(result).toEqual({
        success: true,
        operationId: 'wait-op-1',
        status: CleanupStatus.COMPLETED,
        completed: true,
        startedAt: operation.startedAt,
        completedAt: operation.completedAt
      });
    });

    it('should throw error for non-existent operation', async () => {
      const operations = new Map<string, ICleanupOperation>();

      await expect(
        operationExecutionManager.waitForOperationCompletion('non-existent', operations)
      ).rejects.toThrow('Operation non-existent not found');
    });

    it('should throw error for failed operation', async () => {
      const operationError = new Error('Operation failed');
      const operation: ICleanupOperation = {
        id: 'failed-op',
        type: CleanupOperationType.RESOURCE_CLEANUP,
        priority: CleanupPriority.NORMAL,
        status: CleanupStatus.FAILED,
        componentId: 'test-component',
        operation: jest.fn(),
        error: operationError,
        createdAt: new Date()
      };

      const operations = new Map([['failed-op', operation]]);

      await expect(
        operationExecutionManager.waitForOperationCompletion('failed-op', operations)
      ).rejects.toThrow(operationError);
    });
  });

  describe('waitForAllOperationsCompletion', () => {
    it('should wait for all operations to complete', async () => {
      const runningOperations = new Set<string>();
      const operationQueue: ICleanupOperation[] = [];

      await operationExecutionManager.waitForAllOperationsCompletion(runningOperations, operationQueue);
      // Should complete immediately when no operations are running or queued
    });
  });
});
