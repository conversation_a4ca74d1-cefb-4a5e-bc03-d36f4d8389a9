/**
 * @file CleanupCoordinator Enhanced Integration Tests
 * @filepath shared/src/base/__tests__/CleanupCoordinatorEnhanced.integration.test.ts
 * @component cleanup-coordinator-enhanced-integration-tests
 * @tier T0
 * @context foundation-context
 * @category Integration-Testing-Refactored
 * @created 2025-08-08 16:00:00 +03
 * @modified 2025-08-08 16:00:00 +03
 * 
 * @description
 * Streamlined integration tests for the refactored CleanupCoordinatorEnhanced.
 * Focuses on testing delegation patterns and coordination functionality rather
 * than duplicating detailed testing covered in individual manager test files.
 * 
 * Post-refactoring: Tests coordination layer and public API compatibility.
 * Detailed functionality testing is handled by specialized manager tests:
 * - TimingInfrastructureManager.test.ts
 * - InitializationManager.test.ts  
 * - OperationExecutionManager.test.ts
 * - HealthStatusManager.test.ts
 * - AsyncErrorHandler.test.ts
 */

import {
  CleanupCoordinatorEnhanced,
  createEnhancedCleanupCoordinator,
  getEnhancedCleanupCoordinator
} from '../CleanupCoordinatorEnhanced';
import {
  CleanupOperationType,
  CleanupPriority,
  CleanupStatus,
  ICleanupOperation
} from '../CleanupCoordinatorEnhanced';

// Jest timer mocking for enhanced coordinator tests
jest.useFakeTimers();

describe('CleanupCoordinatorEnhanced - Integration Tests', () => {
  jest.setTimeout(10000);
  let coordinator: CleanupCoordinatorEnhanced;

  beforeEach(async () => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    jest.resetAllMocks();

    coordinator = new CleanupCoordinatorEnhanced({
      testMode: true,
      templateValidationEnabled: true,
      dependencyOptimizationEnabled: true,
      rollbackEnabled: true,
      maxCheckpoints: 3,
      checkpointRetentionDays: 1,
      phaseIntegrationEnabled: false,
      performanceMonitoringEnabled: false,
      maxConcurrentOperations: 1,
      defaultTimeout: 200,
      cleanupIntervalMs: 30000,
      maxRetries: 0
    });

    const initPromise = coordinator.initialize();
    await Promise.race([
      initPromise,
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Coordinator initialization timeout')), 2000)
      )
    ]);

    // Register test operations
    coordinator.registerCleanupOperation('testCleanup', async (component: string) => ({
      success: true,
      cleaned: ['resource1', 'resource2'],
      duration: 50,
      component,
      operation: 'testCleanup',
      timestamp: new Date()
    }));

    coordinator.registerCleanupOperation('performanceCleanup', async (component: string) => ({
      success: true,
      optimized: true,
      cleaned: ['fast-resource'],
      duration: 20,
      component,
      operation: 'performanceCleanup',
      timestamp: new Date()
    }));
  });

  afterEach(async () => {
    if (coordinator) {
      try {
        await coordinator.shutdown();
      } catch (error) {
        // Ignore shutdown errors in tests
      }
    }
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  // ============================================================================
  // INTEGRATION TESTS - FOCUS ON COORDINATION AND DELEGATION
  // ============================================================================

  describe('Initialization and Delegation', () => {
    it('should initialize all managers successfully', async () => {
      // Check that coordinator can provide health status (indicates managers are working)
      const healthStatus = await coordinator.getHealthStatus();
      expect(healthStatus).toBeDefined();
      expect(healthStatus).toHaveProperty('operational');
      expect(healthStatus).toHaveProperty('memoryUsage');
      expect(healthStatus).toHaveProperty('issues');

      // Verify managers are accessible through module status
      const moduleStatus = await coordinator.getModuleStatus();
      expect(moduleStatus).toBeDefined();

      // Check that module status has the expected structure
      expect(typeof moduleStatus).toBe('object');

      // Verify that delegation methods exist (indicates managers are initialized)
      expect(typeof coordinator.getTemplates).toBe('function');
      expect(typeof coordinator.buildDependencyGraph).toBe('function');
      expect(typeof coordinator.createCheckpoint).toBe('function');
      expect(typeof coordinator.getEnhancedMetrics).toBe('function');
    });

    it('should delegate operations to appropriate managers', async () => {
      const operationId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'test-component',
        async () => { /* test operation */ }
      );
      
      expect(operationId).toBeDefined();
      expect(typeof operationId).toBe('string');
    });

    it('should delegate health status checks to HealthStatusManager', async () => {
      const healthStatus = await coordinator.getHealthStatus();
      
      expect(healthStatus).toHaveProperty('operational');
      expect(healthStatus).toHaveProperty('memoryUsage');
      expect(healthStatus).toHaveProperty('issues');
      expect(typeof healthStatus.operational).toBe('boolean');
      expect(typeof healthStatus.memoryUsage).toBe('number');
      expect(Array.isArray(healthStatus.issues)).toBe(true);
    });
  });

  describe('Template System Integration', () => {
    it('should provide template management interface', () => {
      // Test that template management methods exist and are callable
      expect(typeof coordinator.registerTemplate).toBe('function');
      expect(typeof coordinator.getTemplates).toBe('function');
      expect(typeof coordinator.executeTemplate).toBe('function');
      expect(typeof coordinator.getTemplateMetrics).toBe('function');
    });

    it('should get registered templates', () => {
      const templates = coordinator.getTemplates();
      expect(Array.isArray(templates)).toBe(true);
    });

    it('should provide template metrics', () => {
      const metrics = coordinator.getTemplateMetrics();
      expect(metrics).toBeDefined();
    });
  });

  describe('Dependency Resolution Integration', () => {
    it('should build dependency graphs through delegation', () => {
      const operations: ICleanupOperation[] = [
        {
          id: 'op1',
          type: CleanupOperationType.RESOURCE_CLEANUP,
          componentId: 'component1',
          operation: async () => {},
          priority: CleanupPriority.NORMAL,
          timeout: 1000,
          status: CleanupStatus.QUEUED,
          createdAt: new Date(),
          retryCount: 0
        },
        {
          id: 'op2',
          type: CleanupOperationType.RESOURCE_CLEANUP,
          componentId: 'component2',
          operation: async () => {},
          priority: CleanupPriority.NORMAL,
          timeout: 1000,
          status: CleanupStatus.QUEUED,
          createdAt: new Date(),
          retryCount: 0,
          dependencies: ['op1']
        }
      ];

      const graph = coordinator.buildDependencyGraph(operations);
      expect(graph).toBeDefined();
      expect(graph.nodes).toBeDefined();
      expect(graph.edges).toBeDefined();
    });

    it('should analyze dependencies through delegation', async () => {
      const operations: ICleanupOperation[] = [
        {
          id: 'op1',
          type: CleanupOperationType.RESOURCE_CLEANUP,
          componentId: 'component1',
          operation: async () => {},
          priority: CleanupPriority.NORMAL,
          timeout: 1000,
          status: CleanupStatus.QUEUED,
          createdAt: new Date(),
          retryCount: 0
        }
      ];

      const analysis = await coordinator.analyzeDependencies(operations);
      expect(analysis).toBeDefined();
      expect(analysis.hasCycles).toBeDefined();
      expect(analysis.criticalPath).toBeDefined();
    });
  });

  describe('Rollback System Integration', () => {
    it('should create checkpoints through delegation', async () => {
      const operationId = 'test-operation';
      const checkpointId = await coordinator.createCheckpoint(operationId, { test: 'data' });
      
      expect(checkpointId).toBeDefined();
      expect(typeof checkpointId).toBe('string');
    });

    it('should rollback to checkpoints through delegation', async () => {
      const operationId = 'test-operation';
      const checkpointId = await coordinator.createCheckpoint(operationId, { test: 'data' });
      
      await expect(coordinator.rollbackToCheckpoint(checkpointId)).resolves.not.toThrow();
    });

    it('should validate rollback capability through delegation', () => {
      const operationId = 'test-operation';
      const capability = coordinator.validateRollbackCapability(operationId);
      
      expect(capability).toBeDefined();
      expect(capability).toHaveProperty('canRollback');
      expect(capability).toHaveProperty('checkpointAvailable');
    });
  });

  describe('Operation Execution Integration', () => {
    it('should schedule and process operations through delegation', async () => {
      let operationExecuted = false;
      
      const operationId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'test-component',
        async () => {
          operationExecuted = true;
        }
      );

      expect(operationId).toBeDefined();
      
      await coordinator.processQueue();
      await coordinator.waitForCompletion(operationId);
      
      expect(operationExecuted).toBe(true);
    });

    it('should cancel operations through delegation', () => {
      const operationId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'test-component',
        async () => {}
      );

      const cancelled = coordinator.cancelCleanup(operationId);
      expect(cancelled).toBe(true);
    });

    it('should wait for completion through delegation', async () => {
      const operationId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'test-component',
        async () => {}
      );

      await expect(coordinator.waitForCompletion(operationId)).resolves.not.toThrow();
    });
  });

  describe('Enhanced Cleanup Integration', () => {
    it('should provide enhanced cleanup interface', () => {
      // Test that enhanced cleanup method exists and is callable
      expect(typeof coordinator.enhancedCleanup).toBe('function');
    });

    it('should perform enhanced cleanup with rollback capability', async () => {
      const result = await coordinator.enhancedCleanup('rollback-operation', {
        componentId: 'test-component',
        operation: async () => {},
        skipCheckpoint: false
      });

      expect(result).toBeDefined();
    });
  });

  describe('Metrics and Monitoring Integration', () => {
    it('should provide enhanced metrics through delegation', () => {
      const metrics = coordinator.getEnhancedMetrics();

      expect(metrics).toBeDefined();
      expect(metrics).toHaveProperty('templatesRegistered');
      expect(metrics).toHaveProperty('templateMetrics');
      expect(metrics).toHaveProperty('dependencyMetrics');
      expect(metrics).toHaveProperty('rollbackMetrics');
      expect(metrics).toHaveProperty('orchestrationMetrics');
    });

    it('should perform health checks through delegation', async () => {
      const healthCheck = await coordinator.performHealthCheck();

      expect(healthCheck).toBeDefined();
      expect(healthCheck).toHaveProperty('overall');
      expect(healthCheck).toHaveProperty('components');
      expect(['healthy', 'degraded', 'unhealthy']).toContain(healthCheck.overall);
    });
  });

  describe('Component Registry Integration', () => {
    it('should register and manage components through delegation', () => {
      coordinator.registerComponent('test-component', async () => ({
        success: true,
        cleaned: ['test-resource'],
        duration: 10,
        component: 'test-component',
        operation: 'test-operation',
        timestamp: new Date()
      }));

      const components = coordinator.getRegisteredComponents();
      expect(components).toContain('test-component');
    });

    it('should unregister components through delegation', () => {
      coordinator.registerComponent('temp-component', async () => ({
        success: true,
        cleaned: [],
        duration: 0,
        component: 'temp-component',
        operation: 'temp-operation',
        timestamp: new Date()
      }));

      coordinator.unregisterComponent('temp-component');

      // Should not throw - unregister is a no-op in current implementation
      expect(true).toBe(true);
    });
  });

  describe('Factory Functions and Backward Compatibility', () => {
    it('should create enhanced cleanup coordinator via factory function', () => {
      const enhancedCoordinator = createEnhancedCleanupCoordinator({
        testMode: true,
        rollbackEnabled: false
      });

      expect(enhancedCoordinator).toBeInstanceOf(CleanupCoordinatorEnhanced);
    });

    it('should get enhanced cleanup coordinator via getter function', () => {
      const enhancedCoordinator = getEnhancedCleanupCoordinator({
        testMode: true,
        rollbackEnabled: false
      });

      expect(enhancedCoordinator).toBeInstanceOf(CleanupCoordinatorEnhanced);
    });

    it('should maintain backward compatibility with base CleanupCoordinator', async () => {
      let callbackExecuted = false;

      const operationId = coordinator.scheduleCleanup(
        CleanupOperationType.RESOURCE_CLEANUP,
        'compat-component',
        async () => {
          callbackExecuted = true;
        }
      );

      await coordinator.processQueue();
      await coordinator.waitForCompletion(operationId);

      expect(callbackExecuted).toBe(true);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle template execution with non-existent template', async () => {
      await expect(coordinator.executeTemplate('non-existent-template', []))
        .rejects.toThrow();
    });

    it('should handle rollback with non-existent checkpoint', async () => {
      await expect(coordinator.rollbackToCheckpoint('non-existent-checkpoint'))
        .rejects.toThrow();
    });

    it('should handle rollback with no checkpoints for operation', async () => {
      await expect(coordinator.rollbackOperation('non-existent-operation'))
        .rejects.toThrow();
    });

    it('should handle disabled rollback system', async () => {
      const disabledCoordinator = new CleanupCoordinatorEnhanced({
        testMode: true,
        rollbackEnabled: false
      });

      await disabledCoordinator.initialize();

      const result = await disabledCoordinator.enhancedCleanup('test-operation', {
        componentId: 'test-component',
        operation: async () => {}
      });

      expect(result).toBeDefined();

      await disabledCoordinator.shutdown();
    });
  });
});
